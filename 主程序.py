# 修复版main.py - 解决打包后的导入问题
import sys
import os

# 添加当前目录到Python路径
if hasattr(sys, '_MEIPASS'):
    # PyInstaller打包后的临时目录
    base_path = sys._MEIPASS
else:
    # 开发环境
    base_path = os.path.dirname(os.path.abspath(__file__))

sys.path.insert(0, base_path)

# 导入所有必要的模块
try:
    from flask import Flask, render_template, request, jsonify, send_file
    import json
    import requests
    import os
    from datetime import datetime, timedelta
    from urllib.parse import quote, quote_plus, urlparse, parse_qs
    import io
    import threading
    import queue
    import time
    from PIL import Image
    import tempfile
    import zipfile
    import pyperclip
    import webbrowser
    import tkinter as tk
    from tkinter import messagebox
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.hazmat.primitives import hashes, padding
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
    from DrissionPage import Chromium
    import configparser
except ImportError as e:
    print(f"导入模块失败: {e}")
    input("按回车键退出...")
    sys.exit(1)

# 设置模板路径
if hasattr(sys, '_MEIPASS'):
    # PyInstaller打包后的模板路径
    template_folder = os.path.join(sys._MEIPASS, 'templates')
    app = Flask(__name__, template_folder=template_folder)
else:
    # 开发环境
    app = Flask(__name__)

class ConfigManager:
    """配置管理类"""
    def __init__(self):
        self.config_file = "settings.ini"
        self.config = configparser.ConfigParser()
        self.load_config()

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file, encoding='utf-8')
            else:
                # 创建默认配置
                self.create_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.create_default_config()

    def create_default_config(self):
        """创建默认配置"""
        self.config['BLOCKED_WORDS'] = {'words': ''}
        self.save_config()

    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def get_blocked_words(self):
        """获取屏蔽词列表"""
        try:
            words_str = self.config.get('BLOCKED_WORDS', 'words', fallback='')
            if words_str.strip():
                # 使用特殊分隔符来分割屏蔽词
                return [word.strip() for word in words_str.split('|||') if word.strip()]
            return []
        except Exception as e:
            print(f"获取屏蔽词失败: {e}")
            return []

    def set_blocked_words(self, words_list):
        """设置屏蔽词列表"""
        try:
            if not self.config.has_section('BLOCKED_WORDS'):
                self.config.add_section('BLOCKED_WORDS')
            # 使用特殊分隔符来避免配置文件解析问题
            words_str = '|||'.join(words_list)
            self.config.set('BLOCKED_WORDS', 'words', words_str)
            self.save_config()
            return True
        except Exception as e:
            print(f"设置屏蔽词失败: {e}")
            return False

class ImageDownloader:
    """图片下载功能模块"""
    target_path_suffix = r"\导出图\已完成"

    @staticmethod
    def format_size(bytes_size):
        """格式化文件大小"""
        if bytes_size == 0:
            return "0 MB"
        mb_size = bytes_size / (1024 * 1024)
        if mb_size < 1024:
            return f"{mb_size:.2f} MB"
        return f"{mb_size / 1024:.2f} GB"

    @classmethod
    def should_download_file(cls, file_path, file_name):
        """检查文件是否符合下载条件"""
        if cls.target_path_suffix:
            lower_file_path = file_path.lower()
            lower_suffix = cls.target_path_suffix.lower()
            
            if not lower_file_path.endswith(lower_suffix + '\\' + file_name.lower()) and \
               not lower_file_path.endswith(lower_suffix + '/' + file_name.lower()):
                return False
                
        return os.path.splitext(file_name)[1].lower() in ('.png', '.jpg', '.jpeg')

    @classmethod
    def prepare_directory(cls, base_dir):
        """准备下载目录"""
        os.makedirs(base_dir, exist_ok=True)
        return base_dir

class ImageProcessor:
    def __init__(self):
        self.search_base_path = r"E:\图片\原图"
        self.current_output_dir = ""
        self.log_messages = []
        self.config_manager = ConfigManager()

    def log(self, message, message_type='info'):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_messages.append({
            'time': timestamp,
            'message': message,
            'type': message_type
        })
        print(f"[{timestamp}] {message}")

    def remove_blocked_words(self, product_name):
        """从商品名称中移除屏蔽词"""
        blocked_words = self.config_manager.get_blocked_words()
        if not blocked_words:
            return product_name

        original_name = product_name
        cleaned_name = product_name

        for word in blocked_words:
            if word in cleaned_name:
                cleaned_name = cleaned_name.replace(word, '').strip()
                # 清理多余的空格
                while '  ' in cleaned_name:
                    cleaned_name = cleaned_name.replace('  ', ' ')

        if cleaned_name != original_name:
            self.log(f"屏蔽词处理: '{original_name}' -> '{cleaned_name}'", 'info')

        return cleaned_name

    def format_chinese_datetime(self, dt):
        """格式化中文日期时间"""
        year = dt.year
        month = dt.month
        day = dt.day
        hour = dt.hour

        # 判断上午/下午
        if hour < 12:
            period = "上午"
            display_hour = hour if hour > 0 else 12
        else:
            period = "下午"
            display_hour = hour if hour <= 12 else hour - 12
            if display_hour == 0:
                display_hour = 12

        return f"{year}年{month}月{day}日{period}{display_hour}点"

    def get_fulfilment_id(self, order):
        """获取fulfilmentProductSkuId"""
        try:
            sku_list = order.get('skuQuantityDetailList', [])
            if not sku_list:
                self.log("⚠️ skuQuantityDetailList为空", 'warning')
                return "无FulfilmentID"
            return str(sku_list[0].get('fulfilmentProductSkuId', '无FulfilmentID'))
        except Exception as e:
            self.log(f"获取fulfilmentID异常: {str(e)}", 'error')
            return "错误ID"

    def download_image(self, url, save_path):
        """下载图片文件"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.example.com/'
        }

        try:
            response = requests.get(url, headers=headers, stream=True, timeout=15)
            response.raise_for_status()

            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(1024):
                        f.write(chunk)
                return True
            return False
        except Exception as e:
            self.log(f"下载异常: {str(e)}", 'error')
            return False

    def detect_image_extension(self, url):
        """根据URL猜测图片扩展名"""
        if 'jpeg' in url.lower() or 'jpg' in url.lower():
            return '.jpg'
        elif 'png' in url.lower():
            return '.png'
        elif 'gif' in url.lower():
            return '.gif'
        elif 'webp' in url.lower():
            return '.webp'
        return '.jpg'

    def download_api_image(self, url, save_path, max_retries=3, retry_interval=2):
        """下载API图片，带重试机制"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.example.com/',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8'
        }
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    self.log(f"🔄 第 {attempt + 1} 次重试下载API图片...", 'info')
                    time.sleep(retry_interval)
                
                response = requests.get(url, headers=headers, stream=True, timeout=15)
                response.raise_for_status()
                
                if response.status_code == 200:
                    with open(save_path, 'wb') as f:
                        for chunk in response.iter_content(1024):
                            f.write(chunk)
                    self.log(f"✅ API图片下载成功", 'success')
                    return True
            except Exception as e:
                self.log(f"❌ 第 {attempt + 1} 次下载API图片失败: {str(e)}", 'error')
        
        return False

    def search_local_images(self, product_name, search_path="", strict_search=True):
        """搜索本地图片"""
        # 先处理屏蔽词
        cleaned_name = self.remove_blocked_words(product_name)
        # 再移除"2D Flat "前缀
        cleaned_name = cleaned_name.replace("2D Flat ", "").strip()
        search_query = cleaned_name
        
        if search_path and strict_search:
            if not search_path.endswith('\\'):
                search_path += '\\'
            search_query = f"path:\"{search_path}\" {search_query}"
                
        search_params = {
            "search": search_query,
            "json": 1,
            "path_column": 1,
            "size_column": 1,
            "sort": "name",
            "ascending": 1
        }
        
        search_url = "http://localhost:8080/"
        
        try:
            response = requests.get(search_url, params=search_params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            valid_files = []
            for item in data.get("results", []):
                file_name = item.get('name', '')
                file_path = f"{item.get('path', '')}\\{file_name}".replace("\\\\", "\\")
                
                if ImageDownloader.should_download_file(file_path, file_name):
                    valid_files.append({
                        'name': file_name,
                        'path': file_path,
                        'size': item.get('size', 0),
                        'url': f"http://127.0.0.1:8080/{quote(file_path)}"
                    })
            
            return valid_files
            
        except Exception as e:
            self.log(f"❌ 搜索失败: {str(e)}", 'error')
            return []

    def get_product_api_image(self, product_id, json_data):
        """获取商品的API图片URL"""
        try:
            raw_orders = json_data.get('result', {}).get('subOrderForSupplierList', [])
            
            for order in raw_orders:
                if not order.get('isFirst'):
                    continue
                    
                sku_list = order.get('skuQuantityDetailList', [])
                if not sku_list:
                    continue
                    
                sku_id = str(sku_list[0].get('fulfilmentProductSkuId', ''))
                if sku_id == product_id:
                    image_url = order.get('productSkcPicture', '')
                    if image_url:
                        return image_url
            
            return None
        except Exception as e:
            self.log(f"❌ 获取API图片URL失败: {str(e)}", 'error')
            return None

    def process_json_data(self, json_str):
        """处理JSON数据"""
        try:
            data = json.loads(json_str)
            raw_orders = data.get('result', {}).get('subOrderForSupplierList', [])
            original_count = len(raw_orders)

            sub_orders = [order for order in raw_orders if order.get('isFirst')]
            first_order_count = len(sub_orders)

            self.log(f"订单统计: 原始订单数={original_count} | 首单数={first_order_count}", 'info')

            if not sub_orders:
                return None, "未找到首单订单数据"

            current_time = datetime.now()
            chinese_datetime = self.format_chinese_datetime(current_time)
            folder_name = f"商品数据_{chinese_datetime}_共{first_order_count}组"
            os.makedirs(folder_name, exist_ok=True)
            self.current_output_dir = folder_name

            products = []
            success_count = 0

            for index, order in enumerate(sub_orders, 1):
                try:
                    product_name = order.get('productName', '未知商品').strip()
                    sku_id = self.get_fulfilment_id(order)
                    image_url = order.get('productSkcPicture', '').strip()

                    products.append({
                        'name': product_name,
                        'id': sku_id,
                        'api_image_url': image_url
                    })

                    if image_url and sku_id not in ["无FulfilmentID", "错误ID"]:
                        ext = os.path.splitext(image_url)[1].split('?')[0]
                        if not ext:
                            ext = self.detect_image_extension(image_url)

                        img_path = os.path.join(folder_name, f"{sku_id}{ext}")

                        if self.download_image(image_url, img_path):
                            success_count += 1

                except Exception as e:
                    self.log(f"第{index}条处理失败: {str(e)}", 'error')
                    continue

            # 保存商品列表
            txt_path = os.path.join(folder_name, "商品列表.txt")
            with open(txt_path, 'w', encoding='utf-8') as f:
                for product in products:
                    f.write(f"{product['name']}----{product['id']}\n")

            return products, f"解析完成！成功处理: {success_count}/{first_order_count}"

        except Exception as e:
            return None, f"处理失败: {str(e)}"

class JsonDataFetcher:
    """JSON数据拉取功能模块 - 支持多店铺切换"""

    def __init__(self):
        self.log_messages = []
        self.browser = None
        self.tab = None
        self.store_list = []  # 存储店铺信息
        self.auth_headers = None  # 存储认证头信息
        self.current_store_id = None  # 当前选中的店铺ID

    def log(self, message, message_type='info'):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_messages.append({
            'time': timestamp,
            'message': message,
            'type': message_type
        })
        print(f"[{timestamp}] {message}")

    def open_target_page_and_monitor(self):
        """打开目标页面并监控API - 支持多店铺"""
        try:
            self.log("正在连接到浏览器 (端口: 9222)...", 'info')
            self.browser = Chromium(9222)
            self.tab = self.browser.new_tab()
            self.tab.set.activate()
            self.log("浏览器连接成功，新标签页已创建", 'success')

            # 同时监听用户信息API和菜单权限API
            userinfo_api = "api/seller/auth/userInfo"
            menu_api = "api/seller/auth/menu"

            self.log(f"开始监听用户信息API: {userinfo_api}", 'info')
            self.log(f"开始监听菜单权限API: {menu_api}", 'info')

            # 同时监听两个API
            self.tab.listen.start(targets=[userinfo_api, menu_api])

            target_url = "https://agentseller.temu.com/stock/fully-mgt/order-manage-urgency"
            self.log(f"正在访问目标页面: {target_url}", 'info')
            self.tab.get(target_url)
            self.log("页面加载完成，开始监控API请求...", 'success')

            return self.tab, None
        except Exception as e:
            self.log(f"打开页面失败: {str(e)}", 'error')
            return None, None

    def capture_api_requests(self, tab):
        """捕获多个API请求 - 获取店铺信息和认证信息"""
        userinfo_captured = False
        menu_captured = False
        max_attempts = 30  # 最多尝试30次，每次2秒

        try:
            for attempt in range(max_attempts):
                try:
                    packet = tab.listen.wait(timeout=2)
                    if packet:
                        api_url = packet.url

                        if "api/seller/auth/userInfo" in api_url and not userinfo_captured:
                            self.log("捕获到用户信息API请求", 'success')
                            self.process_userinfo_response(packet)
                            userinfo_captured = True

                        elif "api/seller/auth/menu" in api_url and not menu_captured:
                            self.log("捕获到菜单权限API请求", 'success')
                            self.process_menu_response(packet)
                            menu_captured = True

                        # 如果两个API都捕获到了，就可以结束了
                        if userinfo_captured and menu_captured:
                            self.log("所有必要的API信息已捕获完成", 'success')
                            return True

                except Exception as e:
                    if attempt < max_attempts - 1:  # 不是最后一次尝试
                        continue
                    else:
                        self.log(f"监控过程中出现异常: {str(e)}", 'error')

            # 检查是否至少获取到了认证信息
            if menu_captured:
                self.log("已获取认证信息，可以继续操作", 'success')
                return True
            else:
                self.log("未能获取到必要的认证信息", 'error')
                return False

        except Exception as e:
            self.log(f"捕获API请求异常: {str(e)}", 'error')
            return False

    def process_userinfo_response(self, packet):
        """处理用户信息API响应"""
        try:
            if packet.response and packet.response.body:
                # 处理响应数据，可能是字符串或字典格式
                if isinstance(packet.response.body, str):
                    response_data = json.loads(packet.response.body)
                else:
                    response_data = packet.response.body

                self.log("用户信息API响应解析成功", 'success')

                if response_data.get('success') and response_data.get('result'):
                    result = response_data['result']
                    account_id = result.get('accountId')
                    mall_list = result.get('mallList', [])

                    self.log(f"账户ID: {account_id}", 'info')
                    self.log(f"发现 {len(mall_list)} 个店铺", 'success')

                    # 更新店铺列表
                    self.store_list = mall_list

                    # 显示店铺信息
                    for i, mall in enumerate(mall_list, 1):
                        mall_id = mall.get('mallId')
                        mall_name = mall.get('mallName')
                        self.log(f"   {i}. {mall_name} (ID: {mall_id})", 'info')

                else:
                    self.log("用户信息API响应格式异常", 'warning')

        except json.JSONDecodeError as e:
            self.log("用户信息API响应JSON解析失败", 'error')
        except Exception as e:
            self.log(f"处理用户信息API响应时出错: {str(e)}", 'error')

    def process_menu_response(self, packet):
        """处理菜单权限API响应"""
        try:
            self.log("正在提取菜单权限API的认证信息...", 'info')

            # 提取重要的请求头信息
            auth_headers = self.extract_auth_headers(packet)

            if auth_headers:
                # 保存认证信息供后续使用
                self.auth_headers = auth_headers
                self.log("认证信息提取成功", 'success')
                self.log(f"提取到 {len(auth_headers)} 个重要请求头", 'info')

                # 显示关键认证信息（不显示完整内容，只显示存在性）
                key_headers = ['Anti-Content', 'mallid', 'cookie', 'authorization', 'x-csrf-token']
                for header in key_headers:
                    if any(h.lower() == header.lower() for h in auth_headers.keys()):
                        self.log(f"   ✓ {header}: 已获取", 'success')

                # 显示cookie中的mallid信息
                cookie_header = None
                for key, value in auth_headers.items():
                    if key.lower() == 'cookie':
                        cookie_header = value
                        break

                if cookie_header and 'mallid=' in cookie_header:
                    # 提取cookie中的mallid
                    import re
                    mallid_match = re.search(r'mallid=([^;]+)', cookie_header)
                    if mallid_match:
                        current_mallid = mallid_match.group(1)
                        self.log(f"Cookie中的mallid: {current_mallid}", 'info')

            else:
                self.log("未能提取到有效的认证信息", 'warning')

        except Exception as e:
            self.log(f"处理菜单权限API响应时出错: {str(e)}", 'error')

    def extract_auth_headers(self, packet):
        """从API请求中提取认证头信息"""
        if not packet or not packet.request:
            self.log("无效的数据包或请求信息", 'warning')
            return None

        auth_headers = {}
        important_headers = [
            'Anti-Content', 'mallid', 'cookie', 'authorization',
            'x-csrf-token', 'x-requested-with', 'user-agent',
            'accept', 'accept-language', 'content-type',
            'origin', 'referer'
        ]

        if packet.request.headers:
            for key, value in packet.request.headers.items():
                if any(important_header.lower() == key.lower() for important_header in important_headers):
                    clean_key = key.strip()
                    clean_value = str(value).strip()
                    if clean_key and clean_value:
                        auth_headers[clean_key] = clean_value

        return auth_headers

    def send_custom_api_request(self, tab, auth_headers, store_id=None):
        """发送自定义API请求 - 支持指定店铺ID"""
        if not auth_headers:
            self.log("缺少认证头信息", 'error')
            return None

        # 如果指定了店铺ID，修改认证头中的mallid
        modified_headers = auth_headers.copy()
        if store_id:
            modified_headers['mallid'] = str(store_id)
            self.log(f"修改请求头mallid为: {store_id}", 'info')

        custom_request_data = {
            "pageNo": 1,
            "pageSize": 100,
            "urgencyType": 1,
            "isCustomGoods": False,
            "statusList": [1],
            "oneDimensionSort": {
                "firstOrderByParam": "expectLatestDeliverTime",
                "firstOrderByDesc": 0
            }
        }

        api_url = "https://agentseller.temu.com/mms/venom/api/supplier/purchase/manager/querySubOrderList"

        try:
            self.log("开始发送API请求...", 'info')

            # 检查标签页是否有效
            try:
                tab.url
            except Exception as e:
                self.log(f"标签页连接异常，尝试继续: {str(e)}", 'warning')

            js_code = f'''
            (async function() {{
                try {{
                    const response = await fetch('{api_url}', {{
                        method: 'POST',
                        headers: {json.dumps(modified_headers)},
                        body: JSON.stringify({json.dumps(custom_request_data)})
                    }});

                    const responseText = await response.text();

                    const result = {{
                        status: response.status,
                        statusText: response.statusText,
                        headers: Object.fromEntries(response.headers.entries()),
                        body: responseText,
                        success: true
                    }};

                    window.customOrderApiResponse = result;
                    return result;
                }} catch (error) {{
                    const errorResult = {{
                        error: error.message,
                        errorStack: error.stack,
                        success: false
                    }};
                    window.customOrderApiResponse = errorResult;
                    return errorResult;
                }}
            }})();
            '''

            tab.run_js('window.customOrderApiResponse = null;')
            tab.run_js(js_code)

            max_wait_time = 15
            wait_interval = 0.5

            self.log("等待API响应...", 'info')

            for i in range(int(max_wait_time / wait_interval)):
                time.sleep(wait_interval)
                try:
                    response_data = tab.run_js('return window.customOrderApiResponse;')
                    if response_data is not None:
                        self.log("获得API响应", 'success')
                        break
                except Exception:
                    pass
            else:
                self.log("等待响应超时", 'error')
                return None

            if response_data:
                if not response_data.get('success', True) or 'error' in response_data:
                    error_msg = response_data.get('error', '未知错误')
                    self.log(f"JavaScript执行错误: {error_msg}", 'error')
                    return None

                self.log("API请求执行成功", 'success')
                return response_data
            else:
                self.log("未获取到响应数据", 'error')
                return None

        except Exception as e:
            self.log(f"发送API请求时出现异常: {str(e)}", 'error')
            return None

    def fetch_json_data(self, store_id=None):
        """主要的JSON数据拉取方法 - 支持多店铺"""
        try:
            self.log("开始拉取JSON数据...", 'info')
            result = self.open_target_page_and_monitor()

            if result[0]:
                tab = result[0]

                # 捕获多个API请求
                success = self.capture_api_requests(tab)

                if success and self.auth_headers:
                    # 如果是ALL_STORES，应该调用fetch_all_stores_data方法
                    if store_id == 'ALL_STORES':
                        self.log("检测到ALL_STORES请求，但应该调用fetch_all_stores_data方法", 'error')
                        tab.listen.stop()
                        return None, "内部逻辑错误：ALL_STORES应该调用专门的方法"

                    # 如果没有指定店铺ID，使用第一个店铺
                    target_store_id = store_id
                    if not target_store_id and self.store_list:
                        target_store_id = self.store_list[0].get('mallId')
                        store_name = self.store_list[0].get('mallName', '未知店铺')
                        self.log(f"未指定店铺，使用第一个店铺: {store_name} (ID: {target_store_id})", 'info')

                    if target_store_id:
                        self.log(f"开始拉取店铺订单 (店铺ID: {target_store_id})...", 'info')
                        custom_response = self.send_custom_api_request(tab, self.auth_headers, target_store_id)

                        if custom_response and custom_response.get('success'):
                            try:
                                response_json = json.loads(custom_response['body'])
                                self.log("成功获取JSON数据", 'success')

                                # 显示订单统计信息
                                if response_json.get('success'):
                                    result_data = response_json.get('result', {})
                                    total_count = result_data.get('totalCount', 0)
                                    order_list = result_data.get('subOrderForSupplierList', [])
                                    first_orders = [order for order in order_list if order.get('isFirst')]

                                    self.log(f"订单总数: {total_count}", 'info')
                                    self.log(f"当前页订单数: {len(order_list)}", 'info')
                                    self.log(f"首单数量: {len(first_orders)}", 'info')

                                tab.listen.stop()
                                return response_json, "成功拉取JSON数据"
                            except Exception as e:
                                self.log(f"JSON解析失败: {str(e)}", 'error')
                                tab.listen.stop()
                                return None, f"JSON解析失败: {str(e)}"
                        else:
                            self.log("API请求失败", 'error')
                            tab.listen.stop()
                            return None, "API请求失败"
                    else:
                        self.log("未找到可用的店铺ID", 'error')
                        tab.listen.stop()
                        return None, "未找到可用的店铺ID"
                else:
                    self.log("未能获取到必要的认证信息", 'error')
                    if tab:
                        tab.listen.stop()
                    return None, "未能获取到必要的认证信息"
            else:
                self.log("打开目标页面失败", 'error')
                return None, "打开目标页面失败"

        except Exception as e:
            self.log(f"拉取JSON数据异常: {str(e)}", 'error')
            return None, f"拉取JSON数据异常: {str(e)}"

    def fetch_all_stores_data(self):
        """拉取全部店铺的JSON数据并合并"""
        try:
            self.log("开始拉取全部店铺数据...", 'info')
            result = self.open_target_page_and_monitor()

            if result[0]:
                tab = result[0]

                # 捕获多个API请求
                success = self.capture_api_requests(tab)

                if success and self.auth_headers and self.store_list:
                    all_orders = []
                    total_stores = len(self.store_list)

                    self.log(f"开始逐个拉取 {total_stores} 个店铺的订单数据...", 'info')

                    for i, store in enumerate(self.store_list, 1):
                        store_id = store.get('mallId')
                        store_name = store.get('mallName', '未知店铺')

                        self.log(f"正在拉取第 {i}/{total_stores} 个店铺: {store_name} (ID: {store_id})", 'info')

                        custom_response = self.send_custom_api_request(tab, self.auth_headers, store_id)

                        if custom_response and custom_response.get('success'):
                            try:
                                response_json = json.loads(custom_response['body'])

                                if response_json.get('success'):
                                    result_data = response_json.get('result', {})
                                    store_orders = result_data.get('subOrderForSupplierList', [])

                                    # 为每个订单添加店铺信息
                                    for order in store_orders:
                                        order['_storeInfo'] = {
                                            'mallId': store_id,
                                            'mallName': store_name
                                        }

                                    all_orders.extend(store_orders)
                                    self.log(f"店铺 {store_name} 获取到 {len(store_orders)} 个订单", 'success')
                                else:
                                    self.log(f"店铺 {store_name} API返回错误", 'warning')
                            except Exception as e:
                                self.log(f"店铺 {store_name} JSON解析失败: {str(e)}", 'error')
                        else:
                            self.log(f"店铺 {store_name} API请求失败", 'error')

                    # 构建合并后的JSON数据
                    merged_json = {
                        "success": True,
                        "errorCode": 1000000,
                        "errorMsg": None,
                        "result": {
                            "subOrderForSupplierList": all_orders,
                            "total": len(all_orders),
                            "totalCount": len(all_orders),
                            "pageNo": 1,
                            "pageSize": len(all_orders),
                            "_mergedStores": [{"mallId": store.get('mallId'), "mallName": store.get('mallName')} for store in self.store_list]
                        }
                    }

                    # 显示合并统计信息
                    first_orders = [order for order in all_orders if order.get('isFirst')]
                    self.log(f"全部店铺合并完成: 总订单数 {len(all_orders)}, 首单数量 {len(first_orders)}", 'success')

                    tab.listen.stop()
                    return merged_json, f"成功拉取并合并 {total_stores} 个店铺的数据"
                else:
                    self.log("未能获取到必要的认证信息", 'error')
                    if tab:
                        tab.listen.stop()
                    return None, "未能获取到必要的认证信息"
            else:
                self.log("打开目标页面失败", 'error')
                return None, "打开目标页面失败"

        except Exception as e:
            self.log(f"拉取全部店铺数据异常: {str(e)}", 'error')
            return None, f"拉取全部店铺数据异常: {str(e)}"

    def get_store_list(self):
        """获取店铺列表"""
        return self.store_list

    def get_store_info_by_id(self, store_id):
        """根据店铺ID获取店铺信息"""
        for store in self.store_list:
            if str(store.get('mallId')) == str(store_id):
                return store
        return None

    def fetch_single_store_orders_only(self, store_id=None):
        """仅拉取单个店铺的订单数据（需要已有认证信息）"""
        try:
            if not self.auth_headers:
                return None, "缺少认证信息，请先拉取店铺信息和获取认证"

            # 如果没有指定店铺ID，使用第一个店铺
            target_store_id = store_id
            if not target_store_id and self.store_list:
                target_store_id = self.store_list[0].get('mallId')
                store_name = self.store_list[0].get('mallName', '未知店铺')
                self.log(f"未指定店铺，使用第一个店铺: {store_name} (ID: {target_store_id})", 'info')

            if target_store_id:
                self.log(f"开始拉取店铺订单 (店铺ID: {target_store_id})...", 'info')

                # 创建一个临时的tab对象用于发送请求
                if not self.tab:
                    self.log("浏览器连接已断开，尝试重新连接...", 'warning')
                    try:
                        self.browser = Chromium(9222)
                        self.tab = self.browser.new_tab()
                        self.tab.set.activate()
                        self.log("浏览器重新连接成功", 'success')
                    except Exception as e:
                        self.log(f"浏览器重新连接失败: {str(e)}", 'error')
                        return None, f"浏览器连接失败: {str(e)}"

                custom_response = self.send_custom_api_request(self.tab, self.auth_headers, target_store_id)

                if custom_response and custom_response.get('success'):
                    try:
                        response_json = json.loads(custom_response['body'])
                        self.log("成功获取JSON数据", 'success')

                        # 显示订单统计信息
                        if response_json.get('success'):
                            result_data = response_json.get('result', {})
                            total_count = result_data.get('totalCount', 0)
                            order_list = result_data.get('subOrderForSupplierList', [])
                            first_orders = [order for order in order_list if order.get('isFirst')]

                            self.log(f"订单总数: {total_count}", 'info')
                            self.log(f"当前页订单数: {len(order_list)}", 'info')
                            self.log(f"首单数量: {len(first_orders)}", 'info')

                        return response_json, "成功拉取订单JSON数据"
                    except Exception as e:
                        self.log(f"JSON解析失败: {str(e)}", 'error')
                        return None, f"JSON解析失败: {str(e)}"
                else:
                    self.log("API请求失败", 'error')
                    return None, "API请求失败"
            else:
                self.log("未找到可用的店铺ID", 'error')
                return None, "未找到可用的店铺ID"

        except Exception as e:
            self.log(f"拉取订单数据异常: {str(e)}", 'error')
            return None, f"拉取订单数据异常: {str(e)}"

    def fetch_all_stores_orders_only(self):
        """仅拉取全部店铺的订单数据并合并（需要已有认证信息）"""
        try:
            if not self.auth_headers:
                return None, "缺少认证信息，请先拉取店铺信息和获取认证"

            if not self.store_list:
                return None, "缺少店铺信息，请先拉取店铺信息和获取认证"

            all_orders = []
            total_stores = len(self.store_list)

            self.log(f"开始逐个拉取 {total_stores} 个店铺的订单数据...", 'info')

            # 确保浏览器连接可用
            if not self.tab:
                self.log("浏览器连接已断开，尝试重新连接...", 'warning')
                try:
                    self.browser = Chromium(9222)
                    self.tab = self.browser.new_tab()
                    self.tab.set.activate()
                    self.log("浏览器重新连接成功", 'success')
                except Exception as e:
                    self.log(f"浏览器重新连接失败: {str(e)}", 'error')
                    return None, f"浏览器连接失败: {str(e)}"

            for i, store in enumerate(self.store_list, 1):
                store_id = store.get('mallId')
                store_name = store.get('mallName', '未知店铺')

                self.log(f"正在拉取第 {i}/{total_stores} 个店铺: {store_name} (ID: {store_id})", 'info')

                custom_response = self.send_custom_api_request(self.tab, self.auth_headers, store_id)

                if custom_response and custom_response.get('success'):
                    try:
                        response_json = json.loads(custom_response['body'])

                        if response_json.get('success'):
                            result_data = response_json.get('result', {})
                            store_orders = result_data.get('subOrderForSupplierList', [])

                            # 为每个订单添加店铺信息
                            for order in store_orders:
                                order['_storeInfo'] = {
                                    'mallId': store_id,
                                    'mallName': store_name
                                }

                            all_orders.extend(store_orders)
                            self.log(f"店铺 {store_name} 获取到 {len(store_orders)} 个订单", 'success')
                        else:
                            self.log(f"店铺 {store_name} API返回错误", 'warning')
                    except Exception as e:
                        self.log(f"店铺 {store_name} JSON解析失败: {str(e)}", 'error')
                else:
                    self.log(f"店铺 {store_name} API请求失败", 'error')

            # 构建合并后的JSON数据
            merged_json = {
                "success": True,
                "errorCode": 1000000,
                "errorMsg": None,
                "result": {
                    "subOrderForSupplierList": all_orders,
                    "total": len(all_orders),
                    "totalCount": len(all_orders),
                    "pageNo": 1,
                    "pageSize": len(all_orders),
                    "_mergedStores": [{"mallId": store.get('mallId'), "mallName": store.get('mallName')} for store in self.store_list]
                }
            }

            # 显示合并统计信息
            first_orders = [order for order in all_orders if order.get('isFirst')]
            self.log(f"全部店铺合并完成: 总订单数 {len(all_orders)}, 首单数量 {len(first_orders)}", 'success')

            return merged_json, f"成功拉取并合并 {total_stores} 个店铺的订单数据"

        except Exception as e:
            self.log(f"拉取全部店铺订单数据异常: {str(e)}", 'error')
            return None, f"拉取全部店铺订单数据异常: {str(e)}"

def validate_key():
    """验证 key.vdf 文件的有效性"""
    # 获取正确的key.vdf路径
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller打包后，key.vdf应该与exe在同一目录
        exe_dir = os.path.dirname(sys.executable)
        key_file = os.path.join(exe_dir, "key.vdf")
    else:
        # 开发环境
        key_file = "key.vdf"
    
    if not os.path.exists(key_file):
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("错误", f"未找到授权文件 {key_file}")
            root.destroy()
        except:
            print(f"错误: 未找到授权文件 {key_file}")
        sys.exit(1)

    # 密钥生成配置（必须与加密时一致）
    password = os.getenv('KEY_PASSWORD', b'my_super_secret_password')
    salt = os.getenv('KEY_SALT', b'fixed_salt_value')

    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = kdf.derive(password)

    # 读取加密文件
    try:
        with open(key_file, "rb") as f:
            data = f.read()
            iv = data[:16]
            ciphertext = data[16:]

        # 解密数据
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
        decryptor = cipher.decryptor()
        padded_data = decryptor.update(ciphertext) + decryptor.finalize()

        # 移除填充
        unpadder = padding.PKCS7(128).unpadder()
        current_time_bytes = unpadder.update(padded_data) + unpadder.finalize()

        # 验证时间有效性
        stored_time = datetime.fromisoformat(current_time_bytes.decode('utf-8'))

        # 有效期验证（30天）
        if datetime.now() - stored_time > timedelta(days=30):
            try:
                root = tk.Tk()
                root.withdraw()
                messagebox.showerror("错误", "软件授权已过期")
                root.destroy()
            except:
                print("错误: 软件授权已过期")
            sys.exit(1)
            
    except Exception as e:
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("错误", f"授权验证失败: {str(e)}")
            root.destroy()
        except:
            print(f"错误: 授权验证失败: {str(e)}")
        sys.exit(1)

# 全局处理器实例
processor = ImageProcessor()
json_fetcher = JsonDataFetcher()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/parse_json', methods=['POST'])
def parse_json():
    """解析JSON数据"""
    try:
        data = request.get_json()
        json_str = data.get('json_data', '')
        
        if not json_str:
            return jsonify({'success': False, 'message': '请提供JSON数据'})
        
        # 清空之前的日志
        processor.log_messages = []
        
        products, message = processor.process_json_data(json_str)
        
        if products is None:
            return jsonify({
                'success': False, 
                'message': message,
                'logs': processor.log_messages
            })
        
        return jsonify({
            'success': True,
            'message': message,
            'products': products,
            'output_dir': processor.current_output_dir,
            'logs': processor.log_messages
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'系统异常: {str(e)}'})

@app.route('/api/search_images', methods=['POST'])
def search_images():
    """搜索本地图片，并对API图片URL进行代理缩放"""
    try:
        data = request.get_json()
        product_name = data.get('product_name', '')
        product_id = data.get('product_id', '')
        search_path = data.get('search_path', processor.search_base_path)
        strict_search = data.get('strict_search', True)
        json_data = data.get('json_data', {})
        
        if not product_name:
            return jsonify({'success': False, 'message': '请提供商品名称'})
        
        # 搜索本地图片 (URL保持不变)
        local_images = processor.search_local_images(product_name, search_path, strict_search)
        
        # 获取API图片URL
        api_image_url_raw = None
        if json_data and product_id:
            api_image_url_raw = processor.get_product_api_image(product_id, json_data)
        
        # 仅对API图片URL进行代理
        api_image_url_proxied = None
        if api_image_url_raw:
            api_image_url_proxied = f"/api/image_proxy?url={quote_plus(api_image_url_raw)}"
            
        return jsonify({
            'success': True,
            'local_images': local_images,
            'api_image_url': api_image_url_proxied,
            'api_image_url_raw': api_image_url_raw,
            'total_count': len(local_images)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'搜索失败: {str(e)}'})

@app.route('/api/download_selected', methods=['POST'])
def download_selected():
    """下载选中的图片"""
    try:
        data = request.get_json()
        product_id = data.get('product_id', '')
        # 前端现在发送 'type', 'url', 和 'extension'
        image_url = data.get('url', '')
        file_extension = data.get('extension', '.jpg')
        
        if not product_id or not image_url:
            return jsonify({'success': False, 'message': '缺少商品ID或图片URL'})
        
        if not processor.current_output_dir:
            return jsonify({'success': False, 'message': '请先解析JSON数据'})
        
        # 准备下载目录
        base_dir = os.path.join(processor.current_output_dir, "出单图下载")
        target_dir = ImageDownloader.prepare_directory(base_dir)
        new_name = f"{product_id}{file_extension}"
        local_path = os.path.join(target_dir, new_name)
        
        # 在新的UI中，只有本地图片可以被选择和下载
        success = processor.download_image(image_url, local_path)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'图片下载成功: {new_name}',
                'file_path': local_path
            })
        else:
            return jsonify({'success': False, 'message': '图片下载失败'})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'下载失败: {str(e)}'})


@app.route('/api/image_proxy')
def image_proxy():
    """图片代理服务，直接返回原图"""
    image_url = request.args.get('url')
    if not image_url:
        return "缺少图片URL参数", 400

    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(image_url, headers=headers, stream=True, timeout=15)
        response.raise_for_status()

        # 直接返回原图，不进行缩放处理
        img_data = response.content
        
        # 检测图片格式
        try:
            img = Image.open(io.BytesIO(img_data))
            img_format = img.format if img.format else 'JPEG'
            mimetype = f'image/{img_format.lower()}'
        except:
            # 如果无法检测格式，默认为JPEG
            mimetype = 'image/jpeg'
        
        return send_file(io.BytesIO(img_data), mimetype=mimetype)

    except Exception as e:
        print(f"图片代理错误: {e}")
        return "处理图片时出错", 500


@app.route('/api/get_logs')
def get_logs():
    """获取日志信息"""
    return jsonify({'logs': processor.log_messages})

@app.route('/api/get_clipboard', methods=['GET'])
def get_clipboard():
    """获取剪切板内容"""
    try:
        clipboard_content = pyperclip.paste()
        return jsonify({
            'success': True,
            'content': clipboard_content
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取剪切板内容失败: {str(e)}'
        })

@app.route('/api/fetch_store_auth', methods=['POST'])
def fetch_store_auth():
    """拉取店铺信息和获取认证"""
    try:
        # 清空之前的日志
        json_fetcher.log_messages = []

        # 打开页面并获取店铺信息和认证信息
        result = json_fetcher.open_target_page_and_monitor()

        if result[0]:
            tab = result[0]

            # 捕获多个API请求
            success = json_fetcher.capture_api_requests(tab)

            if success:
                # 停止监听
                tab.listen.stop()

                return jsonify({
                    'success': True,
                    'message': '店铺信息和认证获取成功',
                    'logs': json_fetcher.log_messages,
                    'store_list': json_fetcher.get_store_list()
                })
            else:
                if tab:
                    tab.listen.stop()
                return jsonify({
                    'success': False,
                    'message': '未能获取到必要的认证信息',
                    'logs': json_fetcher.log_messages,
                    'store_list': json_fetcher.get_store_list()
                })
        else:
            return jsonify({
                'success': False,
                'message': '打开目标页面失败',
                'logs': json_fetcher.log_messages,
                'store_list': json_fetcher.get_store_list()
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'拉取店铺和认证信息异常: {str(e)}',
            'logs': json_fetcher.log_messages,
            'store_list': json_fetcher.get_store_list()
        })

@app.route('/api/fetch_order_json', methods=['POST'])
def fetch_order_json():
    """拉取订单JSON数据 - 支持多店铺和全部店铺"""
    try:
        data = request.get_json()
        store_id = data.get('store_id') if data else None

        # 清空之前的日志
        json_fetcher.log_messages = []

        # 检查是否已有认证信息
        if not json_fetcher.auth_headers:
            return jsonify({
                'success': False,
                'message': '请先拉取店铺信息和获取认证',
                'logs': json_fetcher.log_messages,
                'store_list': json_fetcher.get_store_list()
            })

        # 判断是否拉取全部店铺
        if store_id == 'ALL_STORES':
            json_data, message = json_fetcher.fetch_all_stores_orders_only()
        else:
            json_data, message = json_fetcher.fetch_single_store_orders_only(store_id)

        if json_data is None:
            return jsonify({
                'success': False,
                'message': message,
                'logs': json_fetcher.log_messages,
                'store_list': json_fetcher.get_store_list()
            })

        # 将JSON数据转换为字符串格式
        json_str = json.dumps(json_data, indent=2, ensure_ascii=False)

        return jsonify({
            'success': True,
            'message': message,
            'json_data': json_str,
            'logs': json_fetcher.log_messages,
            'store_list': json_fetcher.get_store_list()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'拉取订单JSON数据异常: {str(e)}',
            'logs': json_fetcher.log_messages,
            'store_list': json_fetcher.get_store_list()
        })

@app.route('/api/get_store_list', methods=['GET'])
def get_store_list():
    """获取店铺列表"""
    try:
        return jsonify({
            'success': True,
            'store_list': json_fetcher.get_store_list()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取店铺列表失败: {str(e)}'
        })

@app.route('/api/get_blocked_words', methods=['GET'])
def get_blocked_words():
    """获取屏蔽词列表"""
    try:
        blocked_words = processor.config_manager.get_blocked_words()
        return jsonify({
            'success': True,
            'blocked_words': blocked_words
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取屏蔽词失败: {str(e)}'
        })

@app.route('/api/set_blocked_words', methods=['POST'])
def set_blocked_words():
    """设置屏蔽词列表"""
    try:
        data = request.get_json()
        words_text = data.get('words_text', '')

        # 将文本按行分割成列表，过滤空行
        words_list = [word.strip() for word in words_text.split('\n') if word.strip()]

        success = processor.config_manager.set_blocked_words(words_list)

        if success:
            return jsonify({
                'success': True,
                'message': f'屏蔽词设置成功，共 {len(words_list)} 个词',
                'blocked_words': words_list
            })
        else:
            return jsonify({
                'success': False,
                'message': '屏蔽词设置失败'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'设置屏蔽词失败: {str(e)}'
        })

if __name__ == '__main__':
    try:
        # 验证授权文件
        validate_key()
        
        # 启动Flask应用
        port = 8726
        url = f'http://localhost:{port}'
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(1.5)  # 等待Flask服务启动
            webbrowser.open(url)
        
        # 在新线程中打开浏览器
        threading.Thread(target=open_browser, daemon=True).start()
        
        print(f"商品数据管理工具启动中...")
        print(f"服务地址: {url}")
        print(f"浏览器将自动打开，如未打开请手动访问上述地址")
        
        app.run(debug=False, host='0.0.0.0', port=port, use_reloader=False)
        
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        input("按回车键退出...")